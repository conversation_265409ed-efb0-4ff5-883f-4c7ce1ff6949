"use client";

import { useRef } from "react";
import { motion, useInView } from "framer-motion";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

const testimonials = [
    {
        name: "<PERSON>",
        role: "Product Manager, TechCorp",
        content:
            "Working with <PERSON> was an absolute pleasure. His attention to detail and problem-solving skills helped us deliver our project ahead of schedule. I would highly recommend him for any complex web development project.",
        avatar: "/placeholder.svg",
        initials: "<PERSON><PERSON>",
    },
    {
        name: "<PERSON>",
        role: "CTO, Startup Inc.",
        content:
            "<PERSON>'s expertise in Next.js and <PERSON><PERSON> was instrumental in building our platform. He not only delivered high-quality code but also provided valuable insights to improve our product. Truly a top-tier developer.",
        avatar: "/placeholder.svg",
        initials: "<PERSON>",
    },
    {
        name: "<PERSON>",
        role: "UX Designer, Creative Agency",
        content:
            "Collaborating with <PERSON> was seamless. He understood our design vision perfectly and implemented it with pixel-perfect precision. His communication skills and technical abilities make him an invaluable team member.",
        avatar: "/placeholder.svg",
        initials: "ER",
    },
];

export default function Testimonials() {
    const ref = useRef(null);
    const isInView = useInView(ref, { once: true, margin: "-100px" });

    return (
        <section id="testimonials" className="py-20 md:py-32">
            <div className="container px-4 md:px-6">
                <motion.div
                    ref={ref}
                    className="text-center mb-12 md:mb-16"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{
                        opacity: isInView ? 1 : 0,
                        y: isInView ? 0 : 20,
                    }}
                    transition={{ duration: 0.5 }}
                >
                    <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Client Testimonials</h2>
                    <p className="mt-4 text-muted-foreground md:text-xl max-w-3xl mx-auto">
                        What people I've worked with say about me.
                    </p>
                    <div className="w-20 h-1 bg-primary mt-6 mx-auto"></div>
                </motion.div>

                <div className="grid gap-8 md:grid-cols-3">
                    {testimonials.map((testimonial, index) => (
                        <motion.div
                            key={index}
                            initial={{ opacity: 0, y: 50 }}
                            animate={{
                                opacity: isInView ? 1 : 0,
                                y: isInView ? 0 : 50,
                            }}
                            transition={{ duration: 0.5, delay: index * 0.1 }}
                        >
                            <Card className="h-full">
                                <CardHeader>
                                    <div className="flex items-center gap-4">
                                        <Avatar>
                                            <AvatarImage src={testimonial.avatar} />
                                            <AvatarFallback>{testimonial.initials}</AvatarFallback>
                                        </Avatar>
                                        <div>
                                            <CardTitle>{testimonial.name}</CardTitle>
                                            <p className="text-sm text-muted-foreground">{testimonial.role}</p>
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-muted-foreground">"{testimonial.content}"</p>
                                </CardContent>
                            </Card>
                        </motion.div>
                    ))}
                </div>
            </div>
        </section>
    );
}