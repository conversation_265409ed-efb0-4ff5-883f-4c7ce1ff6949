"use client";

import { useRef } from "react";
import { motion, useInView } from "framer-motion";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

const testimonials = [
    {
        name: "SUMAGO INFOTECH Team",
        role: "Full Stack Developer Internship",
        content:
            "During my internship at SUMAGO INFOTECH PVT LTD (Dec 2023 – Feb 2024), I led the development of an E-Commerce website using the MERN stack. As team leader, I guided the project through all development phases and implemented database optimizations that enhanced performance and page load times.",
        avatar: "/placeholder.svg",
        initials: "SI",
    },
    {
        name: "Academic Excellence",
        role: "B.E. (AIDS) Student, DYPIEMR Pune",
        content:
            "Currently pursuing B.E. in Artificial Intelligence and Data Science at Savitribai Phule Pune University with a GPA of 8.67. Coursework includes Data Structures, Analysis of Algorithms, AI, Machine Learning, Deep Learning, IoT, and DBMS. Expected graduation: June 2025.",
        avatar: "/placeholder.svg",
        initials: "AC",
    },
    {
        name: "Competitive Programming",
        role: "Problem Solving & Algorithms",
        content:
            "Active competitive programmer with 200+ problems solved on LeetCode and 300+ problems across various platforms. Participated in Smart India Hackathon (SIH) and Data Wizard Competition. Open Source Contributor during Hacktoberfest 2023.",
        avatar: "/placeholder.svg",
        initials: "CP",
    },
];

export default function Testimonials() {
    const ref = useRef(null);
    const isInView = useInView(ref, { once: true, margin: "-100px" });

    return (
        <section id="testimonials" className="py-20 md:py-32">
            <div className="container px-4 md:px-6">
                <motion.div
                    ref={ref}
                    className="text-center mb-12 md:mb-16"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{
                        opacity: isInView ? 1 : 0,
                        y: isInView ? 0 : 20,
                    }}
                    transition={{ duration: 0.5 }}
                >
                    <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Experience & Achievements</h2>
                    <p className="mt-4 text-muted-foreground md:text-xl max-w-3xl mx-auto">
                        My professional experience, academic journey, and competitive programming achievements.
                    </p>
                    <div className="w-20 h-1 bg-primary mt-6 mx-auto"></div>
                </motion.div>

                <div className="grid gap-8 md:grid-cols-3">
                    {testimonials.map((testimonial, index) => (
                        <motion.div
                            key={index}
                            initial={{ opacity: 0, y: 50 }}
                            animate={{
                                opacity: isInView ? 1 : 0,
                                y: isInView ? 0 : 50,
                            }}
                            transition={{ duration: 0.5, delay: index * 0.1 }}
                        >
                            <Card className="h-full">
                                <CardHeader>
                                    <div className="flex items-center gap-4">
                                        <Avatar>
                                            <AvatarImage src={testimonial.avatar} />
                                            <AvatarFallback>{testimonial.initials}</AvatarFallback>
                                        </Avatar>
                                        <div>
                                            <CardTitle>{testimonial.name}</CardTitle>
                                            <p className="text-sm text-muted-foreground">{testimonial.role}</p>
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-muted-foreground">"{testimonial.content}"</p>
                                </CardContent>
                            </Card>
                        </motion.div>
                    ))}
                </div>
            </div>
        </section>
    );
}