"use client"

import { Sheet, <PERSON><PERSON><PERSON>onte<PERSON>, She<PERSON><PERSON><PERSON>ger } from "./sheet"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Menu } from "lucide-react"
import Link from "next/link"

export default function MobileNav() {
    return (
        <Sheet>
            <SheetTrigger asChild>
                <Button variant="outline" size="icon" className="md:hidden">
                    <Menu className="h-5 w-5" />
                    <span className="sr-only">Toggle menu</span>
                </Button>
            </SheetTrigger>
            <SheetContent side="right">
                <nav className="flex flex-col gap-4">
                    <Link href="#" className="text-sm font-medium">Home</Link>
                    <Link href="#about" className="text-sm font-medium">About</Link>
                    <Link href="#projects" className="text-sm font-medium">Projects</Link>
                    <Link href="#skills" className="text-sm font-medium">Skills</Link>
                    <Link href="#blog" className="text-sm font-medium">Blog</Link>
                    <Link href="#contact" className="text-sm font-medium">Contact</Link>
                </nav>
            </SheetContent>
        </Sheet>
    )
}