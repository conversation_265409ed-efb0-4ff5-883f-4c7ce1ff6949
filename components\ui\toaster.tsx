"use client";

import * as React from "react";
import { toast, ToastBar, Toaster } from "react-hot-toast";
import { cn } from "@/lib/utils";

export const ToasterProvider = () => {
    return (
        <Toaster
            position="bottom-right"
            toastOptions={{
                className: "",
                style: {
                    background: "hsl(var(--background))",
                    color: "hsl(var(--foreground))",
                    border: "1px solid hsl(var(--border))",
                    borderRadius: "calc(var(--radius) - 2px)",
                    padding: "0.75rem 1rem",
                    boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
                },
            }}
        >
            {(t) => (
                <ToastBar
                    toast={t}
                    style={{}}
                    position="bottom-right"
                >
                    {({ icon, message }) => (
                        <div
                            className={cn(
                                "flex items-center gap-3 p-4 font-sans text-sm",
                                t.visible ? "animate-fade-in" : "animate-fade-out"
                            )}
                        >
                            {icon && <span className="shrink-0">{icon}</span>}
                            <span className="flex-1">{message}</span>
                            {t.type !== "loading" && (
                                <button
                                    onClick={() => toast.dismiss(t.id)}
                                    className="ml-2 shrink-0 rounded-full p-1 text-muted-foreground hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="16"
                                        height="16"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    >
                                        <line x1="18" y1="6" x2="6" y2="18"></line>
                                        <line x1="6" y1="6" x2="18" y2="18"></line>
                                    </svg>
                                </button>
                            )}
                        </div>
                    )}
                </ToastBar>
            )}
        </Toaster>
    );
};