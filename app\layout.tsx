import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "@/app/globals.css";
import { ThemeProvider } from "@/components/ui/theme-provider"
import { Toaster } from "sonner";
import ThemeToggle from "@/components/ui/theme-toggle";
import Link from "next/link";
import { Menu } from "lucide-react";
import { Button } from "@/components/ui/button";
import MobileNav from "@/components/ui/mobile-nav";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: {
    default: "Parth Tambe | Full Stack Developer",
    template: "%s | Parth Tambe",
  },
  description: "Portfolio website of Parth Tambe, a Full Stack Developer and B.E. (AIDS) student specializing in modern web technologies and AI/ML.",
  keywords: ["portfolio", "developer", "full stack", "next.js", "react", "python", "machine learning", "AI"],
  authors: [{ name: "Parth Tambe" }],
  creator: "Parth Tambe",
};

export const viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "black" },
  ],
};

const navLinks = [
  { name: "Home", href: "#" },
  { name: "About", href: "#about" },
  { name: "Projects", href: "#projects" },
  { name: "Skills", href: "#skills" },
  { name: "Experience", href: "#testimonials" },
  { name: "Achievements", href: "#blog" },
  { name: "Contact", href: "#contact" },
];

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
          <Header />
          <main className="pt-16">{children}</main>
          <Footer />
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  );
}

function Header() {
  return (
    <header className="fixed top-0 w-full z-50 bg-background/80 backdrop-blur-sm border-b">
      <div className="container flex h-16 items-center justify-between px-4 md:px-6">
        <Link href="/" className="font-bold text-xl">
          Parth<span className="text-primary">Tambe</span>
        </Link>

        <DesktopNav />

        <div className="flex items-center gap-2">
          <ThemeToggle />
          <MobileNav />
        </div>
      </div>
    </header>
  );
}

function DesktopNav() {
  return (
    <nav className="hidden md:flex gap-6">
      {navLinks.map((link) => (
        <Link
          key={link.name}
          href={link.href}
          className="text-sm font-medium hover:text-primary transition-colors"
        >
          {link.name}
        </Link>
      ))}
    </nav>
  );
}

function Footer() {
  return (
    <footer className="border-t py-6 md:py-8">
      <div className="container flex flex-col items-center justify-center gap-4 px-4 md:px-6 text-center">
        <p className="text-sm text-muted-foreground">
          © 2025 Parth Tambe. All rights reserved.
        </p>
        <p className="text-xs text-muted-foreground">
          Built with Next.js, Tailwind CSS, and Framer Motion
        </p>
      </div>
    </footer>
  );
}