"use client"

import * as React from "react"
import { toast } from "sonner"

export interface ToastProps {
    title?: string
    description?: string
    action?: () => void
    actionLabel?: string
}

export const useToast = () => {
    const notify = React.useCallback(({ title, description, action, actionLabel }: ToastProps) => {
        toast(title, {
            description: description,
            action: action ? {
                label: actionLabel || 'Action',
                onClick: action
            } : undefined
        })
    }, [])

    return {
        toast: notify
    }
}