"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON>, Gith<PERSON>, Linkedin, Twitter } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import Image from "next/image";

export default function Hero() {
    const [isVisible, setIsVisible] = useState(false);

    useEffect(() => {
        setIsVisible(true);
    }, []);

    return (
        <section className="relative h-screen flex items-center justify-center overflow-hidden py-20 md:py-32">
            {/* Background gradient */}
            <div className="absolute inset-0 bg-gradient-to-b from-background to-background/50 dark:from-background dark:to-background/80 z-0" />

            {/* Animated background elements */}
            <div className="absolute inset-0 overflow-hidden">
                {Array.from({ length: 20 }).map((_, i) => (
                    <motion.div
                        key={i}
                        className="absolute rounded-full bg-primary/10 dark:bg-primary/5"
                        initial={{
                            x: Math.random() * 100 - 50 + "%",
                            y: Math.random() * 100 + "%",
                            scale: Math.random() * 0.5 + 0.5,
                            opacity: 0,
                        }}
                        animate={{
                            x: [Math.random() * 100 - 50 + "%", Math.random() * 100 - 50 + "%"],
                            y: [Math.random() * 100 + "%", Math.random() * 100 + "%"],
                            opacity: [0.1, 0.3, 0.1],
                            scale: [Math.random() * 0.5 + 0.5, Math.random() * 1 + 1, Math.random() * 0.5 + 0.5],
                        }}
                        transition={{
                            duration: Math.random() * 20 + 10,
                            repeat: Number.POSITIVE_INFINITY,
                            ease: "easeInOut",
                        }}
                        style={{
                            width: `${Math.random() * 300 + 50}px`,
                            height: `${Math.random() * 300 + 50}px`,
                        }}
                    />
                ))}
            </div>

            <div className="container px-4 md:px-6 relative z-10">
                <div className="grid gap-6 lg:grid-cols-[1fr_400px] lg:gap-12 xl:grid-cols-[1fr_600px]">
                    <motion.div
                        className="flex flex-col justify-center space-y-4"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{
                            opacity: isVisible ? 1 : 0,
                            y: isVisible ? 0 : 20,
                        }}
                        transition={{ duration: 0.5, delay: 0.2 }}
                    >
                        <div className="space-y-2">
                            <motion.h1
                                className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none"
                                initial={{ opacity: 0, y: 20 }}
                                animate={{
                                    opacity: isVisible ? 1 : 0,
                                    y: isVisible ? 0 : 20,
                                }}
                                transition={{ duration: 0.5, delay: 0.3 }}
                            >
                                <span className="text-primary">John Doe</span>
                                <span className="block mt-2 text-2xl sm:text-4xl">Full Stack Developer</span>
                            </motion.h1>
                            <motion.p
                                className="max-w-[600px] text-muted-foreground md:text-xl"
                                initial={{ opacity: 0, y: 20 }}
                                animate={{
                                    opacity: isVisible ? 1 : 0,
                                    y: isVisible ? 0 : 20,
                                }}
                                transition={{ duration: 0.5, delay: 0.4 }}
                            >
                                Building beautiful, functional, and performant web experiences that solve real-world problems.
                            </motion.p>
                        </div>
                        <motion.div
                            className="flex flex-col sm:flex-row gap-2 sm:gap-4"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{
                                opacity: isVisible ? 1 : 0,
                                y: isVisible ? 0 : 20,
                            }}
                            transition={{ duration: 0.5, delay: 0.5 }}
                        >
                            <Button size="lg" className="group" asChild>
                                <Link href="#projects">
                                    View My Work
                                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                                </Link>
                            </Button>
                            <Button variant="outline" size="lg" asChild>
                                <Link href="#contact">Hire Me</Link>
                            </Button>
                        </motion.div>
                        <motion.div
                            className="flex items-center gap-4 mt-4"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{
                                opacity: isVisible ? 1 : 0,
                                y: isVisible ? 0 : 20,
                            }}
                            transition={{ duration: 0.5, delay: 0.6 }}
                        >
                            <Link href="https://github.com" target="_blank" rel="noopener noreferrer">
                                <Button variant="ghost" size="icon" aria-label="GitHub">
                                    <Github className="h-5 w-5" />
                                </Button>
                            </Link>
                            <Link href="https://linkedin.com" target="_blank" rel="noopener noreferrer">
                                <Button variant="ghost" size="icon" aria-label="LinkedIn">
                                    <Linkedin className="h-5 w-5" />
                                </Button>
                            </Link>
                            <Link href="https://twitter.com" target="_blank" rel="noopener noreferrer">
                                <Button variant="ghost" size="icon" aria-label="Twitter">
                                    <Twitter className="h-5 w-5" />
                                </Button>
                            </Link>
                        </motion.div>
                    </motion.div>
                    <motion.div
                        className="flex items-center justify-center"
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{
                            opacity: isVisible ? 1 : 0,
                            scale: isVisible ? 1 : 0.9,
                        }}
                        transition={{ duration: 0.5, delay: 0.7 }}
                    >
                        <div className="relative w-[280px] h-[280px] sm:w-[350px] sm:h-[350px] md:w-[400px] md:h-[400px] rounded-full overflow-hidden border-4 border-primary/20">
                            <Image
                                src="/placeholder.svg?height=400&width=400"
                                alt="John Doe"
                                fill
                                className="object-cover"
                                priority
                            />
                        </div>
                    </motion.div>
                </div>
            </div>
        </section>
    );
}