"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON>, Github, Linkedin, Code } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import Image from "next/image";

export default function Hero() {
    const [isVisible, setIsVisible] = useState(false);
    const [isMounted, setIsMounted] = useState(false);

    useEffect(() => {
        setIsMounted(true);
        setIsVisible(true);
    }, []);

    return (
        <section className="relative h-screen flex items-center justify-center overflow-hidden py-20 md:py-32">
            {/* Enhanced Background gradient */}
            <div className="absolute inset-0 bg-gradient-to-br from-background via-background/95 to-primary/5 dark:from-background dark:via-background/90 dark:to-primary/10 z-0" />

            {/* Geometric patterns */}
            <div className="absolute inset-0 opacity-30 dark:opacity-20">
                <div className="absolute top-20 left-10 w-32 h-32 border border-primary/20 rounded-full animate-pulse"></div>
                <div className="absolute top-40 right-20 w-24 h-24 border border-primary/30 rounded-lg rotate-45 animate-bounce"></div>
                <div className="absolute bottom-32 left-1/4 w-16 h-16 bg-primary/10 rounded-full animate-ping"></div>
                <div className="absolute bottom-20 right-1/3 w-20 h-20 border-2 border-primary/25 rounded-full animate-spin"></div>
            </div>

            {/* Animated background elements */}
            {isMounted && (
                <div className="absolute inset-0 overflow-hidden">
                    {Array.from({ length: 20 }).map((_, i) => (
                        <motion.div
                            key={i}
                            className="absolute rounded-full bg-primary/10 dark:bg-primary/5"
                            initial={{
                                x: Math.random() * 100 - 50 + "%",
                                y: Math.random() * 100 + "%",
                                scale: Math.random() * 0.5 + 0.5,
                                opacity: 0,
                            }}
                            animate={{
                                x: [Math.random() * 100 - 50 + "%", Math.random() * 100 - 50 + "%"],
                                y: [Math.random() * 100 + "%", Math.random() * 100 + "%"],
                                opacity: [0.1, 0.3, 0.1],
                                scale: [Math.random() * 0.5 + 0.5, Math.random() * 1 + 1, Math.random() * 0.5 + 0.5],
                            }}
                            transition={{
                                duration: Math.random() * 20 + 10,
                                repeat: Number.POSITIVE_INFINITY,
                                ease: "easeInOut",
                            }}
                            style={{
                                width: `${Math.random() * 300 + 50}px`,
                                height: `${Math.random() * 300 + 50}px`,
                            }}
                        />
                    ))}
                </div>
            )}

            <div className="container px-4 md:px-6 relative z-10">
                <div className="grid gap-6 lg:grid-cols-[1fr_400px] lg:gap-12 xl:grid-cols-[1fr_600px]">
                    <motion.div
                        className="flex flex-col justify-center space-y-4"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{
                            opacity: isVisible ? 1 : 0,
                            y: isVisible ? 0 : 20,
                        }}
                        transition={{ duration: 0.5, delay: 0.2 }}
                    >
                        <div className="space-y-4">
                            <motion.div
                                className="inline-block"
                                initial={{ opacity: 0, scale: 0.5 }}
                                animate={{
                                    opacity: isVisible ? 1 : 0,
                                    scale: isVisible ? 1 : 0.5,
                                }}
                                transition={{ duration: 0.6, delay: 0.2 }}
                            >
                                <span className="px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium border border-primary/20">
                                    Available for Opportunities
                                </span>
                            </motion.div>

                            <motion.h1
                                className="text-4xl font-bold tracking-tight sm:text-6xl xl:text-7xl/none"
                                initial={{ opacity: 0, y: 30 }}
                                animate={{
                                    opacity: isVisible ? 1 : 0,
                                    y: isVisible ? 0 : 30,
                                }}
                                transition={{ duration: 0.7, delay: 0.4 }}
                            >
                                <span className="bg-gradient-to-r from-primary via-primary/80 to-primary/60 bg-clip-text text-transparent">
                                    Parth Tambe
                                </span>
                                <span className="block mt-3 text-3xl sm:text-5xl xl:text-6xl text-foreground/90 font-semibold">
                                    Full Stack Developer
                                </span>
                                <span className="block mt-2 text-xl sm:text-3xl xl:text-4xl text-muted-foreground font-normal">
                                    & AI Engineer
                                </span>
                            </motion.h1>
                            <motion.p
                                className="max-w-[650px] text-muted-foreground md:text-xl leading-relaxed"
                                initial={{ opacity: 0, y: 20 }}
                                animate={{
                                    opacity: isVisible ? 1 : 0,
                                    y: isVisible ? 0 : 20,
                                }}
                                transition={{ duration: 0.6, delay: 0.6 }}
                            >
                                Artificial Intelligence & Data Science Engineering student specializing in full-stack development and machine learning solutions.
                                <span className="text-foreground font-medium"> Experienced in building scalable web applications</span> using modern technologies
                                including React, Next.js, Python, and AI frameworks.
                            </motion.p>
                        </div>
                        <motion.div
                            className="flex flex-col sm:flex-row gap-4 sm:gap-6 mt-8"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{
                                opacity: isVisible ? 1 : 0,
                                y: isVisible ? 0 : 20,
                            }}
                            transition={{ duration: 0.6, delay: 0.8 }}
                        >
                            <Button size="lg" className="group bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 shadow-lg hover:shadow-xl transition-all duration-300" asChild>
                                <Link href="#projects">
                                    View My Work
                                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                                </Link>
                            </Button>
                            <Button variant="outline" size="lg" className="border-2 hover:bg-primary/5 hover:border-primary/50 transition-all duration-300" asChild>
                                <Link href="#contact">Let's Connect</Link>
                            </Button>
                        </motion.div>
                        <motion.div
                            className="flex items-center gap-3 mt-6"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{
                                opacity: isVisible ? 1 : 0,
                                y: isVisible ? 0 : 20,
                            }}
                            transition={{ duration: 0.6, delay: 1.0 }}
                        >
                            <span className="text-sm text-muted-foreground mr-2">Connect:</span>
                            <Link href="https://github.com/tambeparth" target="_blank" rel="noopener noreferrer">
                                <Button variant="ghost" size="icon" className="hover:bg-primary/10 hover:text-primary transition-all duration-300 hover:scale-110" aria-label="GitHub">
                                    <Github className="h-5 w-5" />
                                </Button>
                            </Link>
                            <Link href="https://linkedin.com/in/parthtambe12" target="_blank" rel="noopener noreferrer">
                                <Button variant="ghost" size="icon" className="hover:bg-primary/10 hover:text-primary transition-all duration-300 hover:scale-110" aria-label="LinkedIn">
                                    <Linkedin className="h-5 w-5" />
                                </Button>
                            </Link>
                            <Link href="https://leetcode.com/u/partht12/" target="_blank" rel="noopener noreferrer">
                                <Button variant="ghost" size="icon" className="hover:bg-primary/10 hover:text-primary transition-all duration-300 hover:scale-110" aria-label="LeetCode">
                                    <Code className="h-5 w-5" />
                                </Button>
                            </Link>
                        </motion.div>
                    </motion.div>
                    <motion.div
                        className="flex items-center justify-center"
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{
                            opacity: isVisible ? 1 : 0,
                            scale: isVisible ? 1 : 0.9,
                        }}
                        transition={{ duration: 0.5, delay: 0.7 }}
                    >
                        <div className="relative w-[280px] h-[280px] sm:w-[350px] sm:h-[350px] md:w-[400px] md:h-[400px] rounded-full overflow-hidden border-4 border-primary/20">
                            <Image
                                src="/placeholder.svg?height=400&width=400"
                                alt="Parth Tambe"
                                fill
                                className="object-cover"
                                priority
                            />
                        </div>
                    </motion.div>
                </div>
            </div>
        </section>
    );
}