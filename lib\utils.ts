import { type ClassValue } from "clsx";
import clsx from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * A utility function that merges Tailwind CSS classes
 * @param inputs - Class names to merge
 * @returns Merged class names string
 */
export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs));
}

// Optional additional utilities can be added here
export function formatDate(date: Date): string {
    return new Intl.DateTimeFormat('en-US').format(date);
}