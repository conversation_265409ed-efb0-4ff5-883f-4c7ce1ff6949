"use client";

import { useState, useRef } from "react";
import { motion, useInView } from "framer-motion";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ExternalLink, Github } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

const projects = [
    {
        id: 1,
        title: "TripAI – AI-powered Travel Planner",
        description:
            "A full-stack AI travel assistant using Next.js, Express.js, and FastAPI. Features multi-model NLP chatbot with Hugging Face and Groq APIs, real-time travel support, crisis alerts, personalized itinerary generation, hotel search via Booking.com API, JWT authentication, voice-based chat, and interactive games.",
        image: "/placeholder.svg?height=400&width=600",
        tags: ["Next.js", "Python", "FastAPI", "Express.js", "Hugging Face", "Groq API", "JWT"],
        demoLink: "https://example.com",
        githubLink: "https://github.com/tambeparth",
    },
    {
        id: 2,
        title: "Smart Scrap Collection Platform",
        description: "A full-stack web platform enabling users to find and book nearby scrap collectors using geolocation-based services. Features Firebase Authentication with phone verification, role-based system for customers and collectors, and real-time location tracking.",
        image: "/placeholder.svg?height=400&width=600",
        tags: ["Next.js", "TailwindCSS", "Firebase", "Firestore", "Redux", "GeoFirestore"],
        demoLink: "https://example.com",
        githubLink: "https://github.com/tambeparth",
    },
    {
        id: 3,
        title: "Handwritten Digits Recognition",
        description: "A robust machine learning pipeline for handwritten digit classification using Python and scikit-learn. Experimented with Support Vector Machines, Decision Trees, and Random Forest classifiers, achieving up to 100% accuracy with SVM on training data.",
        image: "/placeholder.svg?height=400&width=600",
        tags: ["Python", "Scikit-learn", "SVM", "Decision Trees", "Random Forest", "Machine Learning"],
        demoLink: "https://example.com",
        githubLink: "https://github.com/tambeparth",
    },
];

export default function Projects() {
    const [hoveredProject, setHoveredProject] = useState<number | null>(null);
    const ref = useRef(null);
    const isInView = useInView(ref, { once: true, margin: "-100px" });

    return (
        <section id="projects" className="py-20 md:py-32">
            <div className="container px-4 md:px-6">
                <motion.div
                    ref={ref}
                    className="text-center mb-12 md:mb-16"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{
                        opacity: isInView ? 1 : 0,
                        y: isInView ? 0 : 20,
                    }}
                    transition={{ duration: 0.5 }}
                >
                    <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">My Projects</h2>
                    <p className="mt-4 text-muted-foreground md:text-xl max-w-3xl mx-auto">
                        Here are some of my recent projects that showcase my skills and expertise.
                    </p>
                    <div className="w-20 h-1 bg-primary mt-6 mx-auto"></div>
                </motion.div>

                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2 xl:gap-8">
                    {projects.map((project, index) => (
                        <motion.div
                            key={project.id}
                            initial={{ opacity: 0, y: 50 }}
                            animate={{
                                opacity: isInView ? 1 : 0,
                                y: isInView ? 0 : 50,
                            }}
                            transition={{ duration: 0.5, delay: index * 0.1 }}
                            onMouseEnter={() => setHoveredProject(project.id)}
                            onMouseLeave={() => setHoveredProject(null)}
                        >
                            <Card className="overflow-hidden h-full transition-all duration-300 hover:shadow-lg">
                                <div className="relative aspect-video overflow-hidden">
                                    <Image
                                        src={project.image || "/placeholder.svg"}
                                        alt={project.title}
                                        fill
                                        className={`object-cover transition-transform duration-500 ${hoveredProject === project.id ? "scale-110" : "scale-100"
                                            }`}
                                    />
                                    <div
                                        className={`absolute inset-0 bg-black/60 flex items-center justify-center gap-4 transition-opacity duration-300 ${hoveredProject === project.id ? "opacity-100" : "opacity-0"
                                            }`}
                                    >
                                        <Button size="sm" variant="secondary" asChild>
                                            <Link href={project.demoLink} target="_blank" rel="noopener noreferrer">
                                                <ExternalLink className="mr-2 h-4 w-4" />
                                                Live Demo
                                            </Link>
                                        </Button>
                                        <Button size="sm" variant="outline" asChild>
                                            <Link href={project.githubLink} target="_blank" rel="noopener noreferrer">
                                                <Github className="mr-2 h-4 w-4" />
                                                Code
                                            </Link>
                                        </Button>
                                    </div>
                                </div>
                                <CardHeader>
                                    <CardTitle>{project.title}</CardTitle>
                                    <CardContent className="p-0 pt-4">
                                        <p className="text-muted-foreground">{project.description}</p>
                                    </CardContent>
                                </CardHeader>
                                <CardFooter>
                                    <div className="flex flex-wrap gap-2">
                                        {project.tags.map((tag) => (
                                            <Badge key={tag} variant="secondary">
                                                {tag}
                                            </Badge>
                                        ))}
                                    </div>
                                </CardFooter>
                            </Card>
                        </motion.div>
                    ))}
                </div>

                <motion.div
                    className="text-center mt-12"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{
                        opacity: isInView ? 1 : 0,
                        y: isInView ? 0 : 20,
                    }}
                    transition={{ duration: 0.5, delay: 0.6 }}
                >
                    <Button size="lg" variant="outline" asChild>
                        <Link href="https://github.com" target="_blank" rel="noopener noreferrer">
                            <Github className="mr-2 h-5 w-5" />
                            View More on GitHub
                        </Link>
                    </Button>
                </motion.div>
            </div>
        </section>
    );
}