"use client";

import { useState, useRef } from "react";
import { motion, useInView, AnimatePresence } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ExternalLink, Github, MoveRight, Sparkles } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

const projects = [
    {
        id: 1,
        title: "TripAI – AI-powered Travel Planner",
        description:
            "A full-stack AI travel assistant using Next.js, Express.js, and FastAPI. Features multi-model NLP chatbot with Hugging Face and Groq APIs, real-time travel support, crisis alerts, personalized itinerary generation, hotel search via Booking.com API, JWT authentication, voice-based chat, and interactive games.",
        image: "/placeholder.svg?height=400&width=600",
        tags: ["Next.js", "Python", "FastAPI", "Express.js", "Hugging Face", "Groq API", "JWT"],
        demoLink: "https://example.com",
        githubLink: "https://github.com/tambeparth",
        accentColor: "from-emerald-500 to-teal-400"
    },
    {
        id: 2,
        title: "Smart Scrap Collection Platform",
        description: "A full-stack web platform enabling users to find and book nearby scrap collectors using geolocation-based services. Features Firebase Authentication with phone verification, role-based system for customers and collectors, and real-time location tracking.",
        image: "/placeholder.svg?height=400&width=600",
        tags: ["Next.js", "TailwindCSS", "Firebase", "Firestore", "Redux", "GeoFirestore"],
        demoLink: "https://example.com",
        githubLink: "https://github.com/tambeparth",
        accentColor: "from-amber-500 to-orange-400"
    },
    {
        id: 3,
        title: "Handwritten Digits Recognition",
        description: "A robust machine learning pipeline for handwritten digit classification using Python and scikit-learn. Experimented with Support Vector Machines, Decision Trees, and Random Forest classifiers, achieving up to 100% accuracy with SVM on training data.",
        image: "/placeholder.svg?height=400&width=600",
        tags: ["Python", "Scikit-learn", "SVM", "Decision Trees", "Random Forest", "Machine Learning"],
        demoLink: "https://example.com",
        githubLink: "https://github.com/tambeparth",
        accentColor: "from-purple-500 to-indigo-400"
    },
];

export default function Projects() {
    const [hoveredProject, setHoveredProject] = useState<number | null>(null);
    const ref = useRef(null);
    const isInView = useInView(ref, { once: true, margin: "-100px" });

    return (
        <section id="projects" className="relative py-20 md:py-32 overflow-hidden">
            {/* Animated background elements */}
            <div className="absolute inset-0 overflow-hidden opacity-20 pointer-events-none">
                <motion.div
                    className="absolute top-1/4 left-1/4 w-64 h-64 rounded-full bg-emerald-500/20 blur-3xl"
                    animate={{
                        x: [0, 50, 0],
                        y: [0, -30, 0],
                    }}
                    transition={{
                        duration: 15,
                        repeat: Infinity,
                        ease: "easeInOut"
                    }}
                />
                <motion.div
                    className="absolute bottom-1/4 right-1/4 w-96 h-96 rounded-full bg-purple-500/20 blur-3xl"
                    animate={{
                        x: [0, -60, 0],
                        y: [0, 40, 0],
                    }}
                    transition={{
                        duration: 20,
                        repeat: Infinity,
                        ease: "easeInOut",
                        delay: 2
                    }}
                />
            </div>

            <div className="container px-4 md:px-6 relative">
                <motion.div
                    ref={ref}
                    className="text-center mb-16 md:mb-20"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{
                        opacity: isInView ? 1 : 0,
                        y: isInView ? 0 : 30,
                    }}
                    transition={{ duration: 0.6 }}
                >
                    <motion.div
                        className="inline-flex items-center gap-2 mb-4"
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{
                            opacity: isInView ? 1 : 0,
                            scale: isInView ? 1 : 0.8,
                        }}
                        transition={{ duration: 0.5, delay: 0.2 }}
                    >
                        <Sparkles className="w-5 h-5 text-primary" />
                        <span className="px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium border border-primary/20">
                            Portfolio Showcase
                        </span>
                        <Sparkles className="w-5 h-5 text-primary" />
                    </motion.div>

                    <motion.h2
                        className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{
                            opacity: isInView ? 1 : 0,
                            y: isInView ? 0 : 20,
                        }}
                        transition={{ duration: 0.5, delay: 0.3 }}
                    >
                        <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                            Featured
                        </span>{" "}
                        <span className="bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
                            Projects
                        </span>
                    </motion.h2>

                    <motion.p
                        className="mt-6 text-muted-foreground md:text-xl max-w-3xl mx-auto leading-relaxed"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{
                            opacity: isInView ? 1 : 0,
                            y: isInView ? 0 : 20,
                        }}
                        transition={{ duration: 0.5, delay: 0.4 }}
                    >
                        A <span className="text-primary font-medium">curated collection</span> of my recent work in{" "}
                        <span className="text-foreground font-medium">web development</span>,{" "}
                        <span className="text-foreground font-medium">artificial intelligence</span>, and{" "}
                        <span className="text-foreground font-medium">software engineering</span>.
                    </motion.p>

                    <motion.div
                        className="w-24 h-1 bg-gradient-to-r from-primary to-primary/50 mt-8 mx-auto rounded-full"
                        initial={{ scaleX: 0 }}
                        animate={{
                            scaleX: isInView ? 1 : 0,
                        }}
                        transition={{ duration: 0.8, delay: 0.5, ease: [0.22, 1, 0.36, 1] }}
                    />
                </motion.div>

                <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                    <AnimatePresence>
                        {projects.map((project, index) => (
                            <motion.div
                                key={project.id}
                                initial={{ opacity: 0, y: 50, scale: 0.95 }}
                                animate={{
                                    opacity: isInView ? 1 : 0,
                                    y: isInView ? 0 : 50,
                                    scale: isInView ? 1 : 0.95,
                                }}
                                transition={{
                                    duration: 0.6,
                                    delay: index * 0.15,
                                    type: "spring",
                                    stiffness: 100,
                                    damping: 10
                                }}
                                whileHover={{ y: -10 }}
                                onMouseEnter={() => setHoveredProject(project.id)}
                                onMouseLeave={() => setHoveredProject(null)}
                                className="relative"
                            >
                                {/* Glow effect */}
                                <div className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${project.accentColor} opacity-0 group-hover:opacity-20 blur-md transition-opacity duration-500 -z-10`} />

                                <Card className="h-full overflow-hidden border border-border/50 bg-card/50 backdrop-blur-sm transition-all duration-500 group hover:border-primary/30 hover:shadow-xl hover:shadow-primary/10">
                                    <div className="relative aspect-video overflow-hidden">
                                        <Image
                                            src={project.image || "/placeholder.svg"}
                                            alt={project.title}
                                            fill
                                            className={`object-cover transition-transform duration-700 ${hoveredProject === project.id ? "scale-105" : "scale-100"}`}
                                        />
                                        <div
                                            className={`absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-transparent flex items-end p-6 transition-opacity duration-500 ${hoveredProject === project.id ? "opacity-100" : "opacity-0"}`}
                                        >
                                            <div className="flex gap-3 w-full">
                                                <Button
                                                    size="sm"
                                                    className="flex-1 transition-all duration-300 hover:shadow-lg hover:shadow-primary/20"
                                                    asChild
                                                >
                                                    <Link href={project.demoLink} target="_blank" rel="noopener noreferrer">
                                                        <ExternalLink className="mr-2 h-4 w-4" />
                                                        Live Demo
                                                    </Link>
                                                </Button>
                                                <Button
                                                    size="sm"
                                                    variant="outline"
                                                    className="flex-1 transition-all duration-300 hover:shadow-lg"
                                                    asChild
                                                >
                                                    <Link href={project.githubLink} target="_blank" rel="noopener noreferrer">
                                                        <Github className="mr-2 h-4 w-4" />
                                                        Code
                                                    </Link>
                                                </Button>
                                            </div>
                                        </div>
                                    </div>

                                    <CardHeader>
                                        <CardTitle className="text-xl font-bold tracking-tight">
                                            {project.title}
                                        </CardTitle>
                                    </CardHeader>

                                    <CardContent className="pb-0">
                                        <p className="text-muted-foreground">{project.description}</p>
                                    </CardContent>

                                    <CardFooter className="flex flex-col items-start gap-4 pt-6">
                                        <div className="flex flex-wrap gap-2">
                                            {project.tags.map((tag) => (
                                                <motion.div
                                                    key={tag}
                                                    whileHover={{ scale: 1.05 }}
                                                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                                                >
                                                    <Badge
                                                        variant="secondary"
                                                        className="font-medium hover:bg-primary/10 hover:text-primary transition-colors"
                                                    >
                                                        {tag}
                                                    </Badge>
                                                </motion.div>
                                            ))}
                                        </div>
                                    </CardFooter>
                                </Card>
                            </motion.div>
                        ))}
                    </AnimatePresence>
                </div>

                <motion.div
                    className="text-center mt-16"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{
                        opacity: isInView ? 1 : 0,
                        y: isInView ? 0 : 20,
                    }}
                    transition={{ duration: 0.5, delay: 0.8 }}
                >
                    <Button
                        size="lg"
                        variant="outline"
                        className="group px-8 hover:shadow-lg hover:shadow-primary/10 transition-all"
                        asChild
                    >
                        <Link href="https://github.com" target="_blank" rel="noopener noreferrer">
                            <Github className="mr-3 h-5 w-5" />
                            View More on GitHub
                            <MoveRight className="ml-3 h-5 w-5 opacity-0 group-hover:opacity-100 translate-x-0 group-hover:translate-x-1 transition-all duration-300" />
                        </Link>
                    </Button>
                </motion.div>
            </div>
        </section>
    );
}