"use client";

export default function PlaceholderImage({ width, height }: { width: number; height: number }) {
    return `data:image/svg+xml,%3Csvg width='${width}' height='${height}' viewBox='0 0 ${width} ${height}' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='${width}' height='${height}' fill='%23F3F4F6'/%3E%3Cpath d='M${width / 2 - 20},${height / 2 - 20} h40 v40 h-40 v-40 Z' fill='%23E5E7EB'/%3E%3C/svg%3E`;
}