"use client";

import { motion, useInView } from "framer-motion";
import { useRef } from "react";
import { Button } from "@/components/ui/button";
import { FileDown } from "lucide-react";
import Image from "next/image";

export default function About() {
    const ref = useRef(null);
    const isInView = useInView(ref, { once: true, margin: "-100px" });

    return (
        <section id="about" className="py-20 md:py-32 bg-muted/50">
            <div className="container px-4 md:px-6">
                <motion.div
                    ref={ref}
                    className="grid gap-10 lg:grid-cols-2 lg:gap-16 items-center"
                    initial={{ opacity: 0, y: 50 }}
                    animate={{
                        opacity: isInView ? 1 : 0,
                        y: isInView ? 0 : 50,
                    }}
                    transition={{ duration: 0.5, staggerChildren: 0.1 }}
                >
                    <div className="relative overflow-hidden rounded-xl">
                        <motion.div
                            initial={{ scale: 1.2, opacity: 0 }}
                            animate={{
                                scale: isInView ? 1 : 1.2,
                                opacity: isInView ? 1 : 0,
                            }}
                            transition={{ duration: 0.7 }}
                        >
                            <Image
                                src="/placeholder.svg?height=600&width=600"
                                alt="About Me"
                                width={600}
                                height={600}
                                className="object-cover w-full h-auto rounded-xl"
                            />
                        </motion.div>
                    </div>
                    <div className="space-y-6">
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{
                                opacity: isInView ? 1 : 0,
                                y: isInView ? 0 : 20,
                            }}
                            transition={{ duration: 0.5, delay: 0.2 }}
                        >
                            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">About Me</h2>
                            <div className="w-20 h-1 bg-primary mt-4 mb-6"></div>
                        </motion.div>

                        <motion.p
                            className="text-muted-foreground leading-relaxed"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{
                                opacity: isInView ? 1 : 0,
                                y: isInView ? 0 : 20,
                            }}
                            transition={{ duration: 0.5, delay: 0.3 }}
                        >
                            I'm a passionate Full Stack Developer with over 5 years of experience building web applications that solve
                            real-world problems. My journey in tech started when I built my first website at 15, and I've been hooked
                            ever since.
                        </motion.p>

                        <motion.p
                            className="text-muted-foreground leading-relaxed"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{
                                opacity: isInView ? 1 : 0,
                                y: isInView ? 0 : 20,
                            }}
                            transition={{ duration: 0.5, delay: 0.4 }}
                        >
                            Currently, I work as a Senior Developer at Tech Innovations Inc., where I lead a team building scalable
                            web applications using Next.js, React, and Node.js. I'm passionate about creating intuitive user
                            experiences and writing clean, maintainable code.
                        </motion.p>

                        <motion.p
                            className="text-muted-foreground leading-relaxed"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{
                                opacity: isInView ? 1 : 0,
                                y: isInView ? 0 : 20,
                            }}
                            transition={{ duration: 0.5, delay: 0.5 }}
                        >
                            When I'm not coding, you'll find me hiking, reading sci-fi novels, or experimenting with new technologies.
                            I believe in continuous learning and sharing knowledge with the community.
                        </motion.p>

                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{
                                opacity: isInView ? 1 : 0,
                                y: isInView ? 0 : 20,
                            }}
                            transition={{ duration: 0.5, delay: 0.6 }}
                        >
                            <Button className="mt-4 group">
                                Download Resume
                                <FileDown className="ml-2 h-4 w-4 transition-transform group-hover:translate-y-1" />
                            </Button>
                        </motion.div>
                    </div>
                </motion.div>
            </div>
        </section>
    );
}