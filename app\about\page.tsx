"use client";

import { motion, useInView } from "framer-motion";
import { useRef } from "react";
import { Button } from "@/components/ui/button";
import { FileDown } from "lucide-react";
import Image from "next/image";

export default function About() {
    const ref = useRef(null);
    const isInView = useInView(ref, { once: true, margin: "-100px" });

    return (
        <section id="about" className="py-20 md:py-32 bg-muted/50">
            <div className="container px-4 md:px-6">
                <motion.div
                    ref={ref}
                    className="grid gap-10 lg:grid-cols-2 lg:gap-16 items-center"
                    initial={{ opacity: 0, y: 50 }}
                    animate={{
                        opacity: isInView ? 1 : 0,
                        y: isInView ? 0 : 50,
                    }}
                    transition={{ duration: 0.5, staggerChildren: 0.1 }}
                >
                    <div className="relative overflow-hidden rounded-xl">
                        <motion.div
                            initial={{ scale: 1.2, opacity: 0 }}
                            animate={{
                                scale: isInView ? 1 : 1.2,
                                opacity: isInView ? 1 : 0,
                            }}
                            transition={{ duration: 0.7 }}
                        >
                            <Image
                                src="/placeholder.svg?height=600&width=600"
                                alt="About Me"
                                width={600}
                                height={600}
                                className="object-cover w-full h-auto rounded-xl"
                            />
                        </motion.div>
                    </div>
                    <div className="space-y-6">
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{
                                opacity: isInView ? 1 : 0,
                                y: isInView ? 0 : 20,
                            }}
                            transition={{ duration: 0.5, delay: 0.2 }}
                        >
                            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">About Me</h2>
                            <div className="w-20 h-1 bg-primary mt-4 mb-6"></div>
                        </motion.div>

                        <motion.p
                            className="text-muted-foreground leading-relaxed"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{
                                opacity: isInView ? 1 : 0,
                                y: isInView ? 0 : 20,
                            }}
                            transition={{ duration: 0.5, delay: 0.3 }}
                        >
                            I'm a passionate Full Stack Developer and B.E. (AIDS) student at Savitribai Phule Pune University (DYPIEMR),
                            Pune, with a strong foundation in both web development and artificial intelligence. Currently maintaining a
                            GPA of 8.67, I'm set to graduate in June 2025.
                        </motion.p>

                        <motion.p
                            className="text-muted-foreground leading-relaxed"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{
                                opacity: isInView ? 1 : 0,
                                y: isInView ? 0 : 20,
                            }}
                            transition={{ duration: 0.5, delay: 0.4 }}
                        >
                            I gained valuable industry experience as a Full Stack Developer Intern at SUMAGO INFOTECH PVT LTD, where I
                            led the development of an E-Commerce website using the MERN stack. As team leader, I guided the project
                            through all development phases and implemented database optimizations that significantly improved performance.
                        </motion.p>

                        <motion.p
                            className="text-muted-foreground leading-relaxed"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{
                                opacity: isInView ? 1 : 0,
                                y: isInView ? 0 : 20,
                            }}
                            transition={{ duration: 0.5, delay: 0.5 }}
                        >
                            My expertise spans across Python, C++, JavaScript, React.js, Next.js, and machine learning technologies.
                            I'm an active problem solver with 200+ questions solved on LeetCode and have participated in various
                            hackathons including Smart India Hackathon. I'm passionate about building AI-powered solutions and
                            contributing to open-source projects.
                        </motion.p>

                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{
                                opacity: isInView ? 1 : 0,
                                y: isInView ? 0 : 20,
                            }}
                            transition={{ duration: 0.5, delay: 0.6 }}
                        >
                            <Button className="mt-4 group">
                                Download Resume
                                <FileDown className="ml-2 h-4 w-4 transition-transform group-hover:translate-y-1" />
                            </Button>
                        </motion.div>
                    </div>
                </motion.div>
            </div>
        </section>
    );
}