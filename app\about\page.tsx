"use client";

import { motion, useInView, AnimatePresence } from "framer-motion";
import { useRef } from "react";
import { Button } from "@/components/ui/button";
import { FileDown, Award, Code, Cpu, Database } from "lucide-react";

export default function About() {
    const ref = useRef(null);
    const isInView = useInView(ref, { once: true, margin: "-100px" });

    const skills = [
        { name: "Full Stack Dev", icon: <Code className="w-5 h-5" /> },
        { name: "AI/ML", icon: <Cpu className="w-5 h-5" /> },
        { name: "Data Science", icon: <Database className="w-5 h-5" /> },
        { name: "Competitive Coding", icon: <Award className="w-5 h-5" /> }
    ];

    return (
        <section id="about" className="py-20 md:py-32 bg-gradient-to-b from-muted/20 to-background relative overflow-hidden">
            {/* Decorative elements */}
            <div className="absolute top-0 left-0 w-full h-full overflow-hidden opacity-10">
                <div className="absolute -top-20 -left-20 w-64 h-64 rounded-full bg-primary/30 blur-3xl"></div>
                <div className="absolute bottom-0 right-0 w-96 h-96 rounded-full bg-secondary/30 blur-3xl"></div>
            </div>

            <div className="container px-4 md:px-6 relative">
                <motion.div
                    ref={ref}
                    className="grid gap-10 lg:grid-cols-2 lg:gap-16 items-center"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: isInView ? 1 : 0 }}
                    transition={{ duration: 0.5 }}
                >
                    <div className="relative">
                        {/* Interactive Stats Cards */}
                        <motion.div
                            className="grid grid-cols-2 gap-6 mb-8"
                            initial={{ opacity: 0, y: 30 }}
                            animate={{
                                opacity: isInView ? 1 : 0,
                                y: isInView ? 0 : 30,
                            }}
                            transition={{ duration: 0.6, delay: 0.2 }}
                        >
                            {[
                                { number: "8.67", label: "GPA", suffix: "/10", color: "from-emerald-500 to-teal-400" },
                                { number: "200", label: "LeetCode Problems", suffix: "+", color: "from-blue-500 to-cyan-400" },
                                { number: "3", label: "Major Projects", suffix: "", color: "from-purple-500 to-pink-400" },
                                { number: "2024", label: "Expected Graduation", suffix: "", color: "from-orange-500 to-red-400" }
                            ].map((stat, index) => (
                                <motion.div
                                    key={stat.label}
                                    className="relative group"
                                    initial={{ opacity: 0, scale: 0.8 }}
                                    animate={{
                                        opacity: isInView ? 1 : 0,
                                        scale: isInView ? 1 : 0.8,
                                    }}
                                    transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
                                    whileHover={{ scale: 1.05 }}
                                >
                                    <div className="relative p-6 rounded-2xl bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-sm border border-border/50 hover:border-primary/30 transition-all duration-300 overflow-hidden">
                                        <div className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-0 group-hover:opacity-10 transition-opacity duration-300`}></div>
                                        <div className="relative z-10">
                                            <motion.div
                                                className="text-3xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent"
                                                initial={{ opacity: 0 }}
                                                animate={{ opacity: isInView ? 1 : 0 }}
                                                transition={{ duration: 0.8, delay: 0.5 + index * 0.1 }}
                                            >
                                                {stat.number}{stat.suffix}
                                            </motion.div>
                                            <div className="text-sm text-muted-foreground mt-1 font-medium">
                                                {stat.label}
                                            </div>
                                        </div>
                                        <div className={`absolute -bottom-2 -right-2 w-16 h-16 bg-gradient-to-br ${stat.color} rounded-full opacity-20 group-hover:opacity-30 transition-opacity duration-300`}></div>
                                    </div>
                                </motion.div>
                            ))}
                        </motion.div>

                        {/* Tech Stack Visualization */}
                        <motion.div
                            className="relative p-8 rounded-2xl bg-gradient-to-br from-card/60 to-card/20 backdrop-blur-sm border border-border/50 overflow-hidden"
                            initial={{ opacity: 0, y: 30 }}
                            animate={{
                                opacity: isInView ? 1 : 0,
                                y: isInView ? 0 : 30,
                            }}
                            transition={{ duration: 0.6, delay: 0.4 }}
                        >
                            <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent"></div>
                            <div className="relative z-10">
                                <h3 className="text-xl font-semibold mb-6 text-center">Core Technologies</h3>
                                <div className="flex flex-wrap gap-3 justify-center">
                                    {[
                                        { name: "React", level: 90, color: "bg-blue-500" },
                                        { name: "Next.js", level: 85, color: "bg-gray-800" },
                                        { name: "Python", level: 88, color: "bg-yellow-500" },
                                        { name: "JavaScript", level: 87, color: "bg-yellow-400" },
                                        { name: "Node.js", level: 80, color: "bg-green-500" },
                                        { name: "AI/ML", level: 75, color: "bg-purple-500" }
                                    ].map((tech, index) => (
                                        <motion.div
                                            key={tech.name}
                                            className="group relative"
                                            initial={{ opacity: 0, scale: 0 }}
                                            animate={{
                                                opacity: isInView ? 1 : 0,
                                                scale: isInView ? 1 : 0,
                                            }}
                                            transition={{ duration: 0.4, delay: 0.6 + index * 0.1 }}
                                            whileHover={{ scale: 1.1 }}
                                        >
                                            <div className="px-4 py-2 rounded-full bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 hover:border-primary/40 transition-all duration-300 cursor-pointer">
                                                <span className="text-sm font-medium">{tech.name}</span>
                                            </div>
                                            <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-foreground text-background px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
                                                {tech.level}% Proficiency
                                            </div>
                                        </motion.div>
                                    ))}
                                </div>
                            </div>

                            {/* Floating elements */}
                            <div className="absolute top-4 right-4 w-8 h-8 rounded-full bg-primary/20 animate-pulse"></div>
                            <div className="absolute bottom-6 left-6 w-6 h-6 rounded-full bg-secondary/30 animate-bounce"></div>
                            <div className="absolute top-1/2 left-4 w-4 h-4 rounded-full bg-accent/40 animate-ping"></div>
                        </motion.div>
                    </div>

                    <div className="space-y-8">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{
                                opacity: isInView ? 1 : 0,
                                y: isInView ? 0 : 30,
                            }}
                            transition={{
                                duration: 0.6,
                                type: "spring",
                                stiffness: 100
                            }}
                        >
                            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
                                About Me
                            </h2>
                            <div className="w-20 h-1.5 bg-gradient-to-r from-primary to-secondary mt-4 mb-6 rounded-full"></div>
                        </motion.div>

                        <AnimatePresence>
                            <motion.div
                                className="space-y-6"
                                initial={{ opacity: 0 }}
                                animate={{ opacity: isInView ? 1 : 0 }}
                                transition={{ staggerChildren: 0.1 }}
                            >
                                <motion.p
                                    className="text-muted-foreground leading-relaxed text-lg"
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{
                                        opacity: isInView ? 1 : 0,
                                        x: isInView ? 0 : -20,
                                    }}
                                    transition={{ duration: 0.5 }}
                                >
                                    I'm a <span className="text-primary font-medium">Full Stack Developer</span> and <span className="text-secondary font-medium">AI Engineer</span> currently pursuing my Bachelor's in Artificial Intelligence & Data Science at Savitribai Phule Pune University. With a strong academic foundation (GPA: 8.67) and hands-on industry experience, I bridge the gap between cutting-edge AI research and practical web application development.
                                </motion.p>

                                <motion.p
                                    className="text-muted-foreground leading-relaxed text-lg"
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{
                                        opacity: isInView ? 1 : 0,
                                        x: isInView ? 0 : -20,
                                    }}
                                    transition={{ duration: 0.5, delay: 0.1 }}
                                >
                                    As a <span className="font-medium">Full Stack Developer Intern</span> at SUMAGO INFOTECH, I led the development of a high-performance E-Commerce platform using the MERN stack. My leadership in the complete SDLC and database optimizations resulted in a <span className="font-medium">40% improvement</span> in system performance and enhanced user experience metrics.
                                </motion.p>

                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{
                                        opacity: isInView ? 1 : 0,
                                        y: isInView ? 0 : 20,
                                    }}
                                    transition={{ duration: 0.5, delay: 0.2 }}
                                    className="grid grid-cols-2 gap-4 pt-2"
                                >
                                    {skills.map((skill, index) => (
                                        <motion.div
                                            key={index}
                                            className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg border border-border hover:bg-muted transition-colors"
                                            whileHover={{ y: -3 }}
                                        >
                                            <div className="p-2 rounded-md bg-primary/10 text-primary">
                                                {skill.icon}
                                            </div>
                                            <span className="font-medium">{skill.name}</span>
                                        </motion.div>
                                    ))}
                                </motion.div>

                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{
                                        opacity: isInView ? 1 : 0,
                                        y: isInView ? 0 : 20,
                                    }}
                                    transition={{ duration: 0.5, delay: 0.3 }}
                                    className="pt-4"
                                >
                                    <Button className="group relative overflow-hidden">
                                        <span className="relative z-10 flex items-center">
                                            Download Resume
                                            <FileDown className="ml-2 h-4 w-4 transition-transform group-hover:translate-y-1" />
                                        </span>
                                        <span className="absolute inset-0 bg-gradient-to-r from-primary to-secondary opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                                    </Button>
                                </motion.div>
                            </motion.div>
                        </AnimatePresence>
                    </div>
                </motion.div>
            </div>
        </section>
    );
}