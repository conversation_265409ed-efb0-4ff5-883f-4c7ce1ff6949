"use client";

import { motion, useInView } from "framer-motion";
import { useRef } from "react";
import { Button } from "@/components/ui/button";
import { FileDown } from "lucide-react";
import Image from "next/image";

export default function About() {
    const ref = useRef(null);
    const isInView = useInView(ref, { once: true, margin: "-100px" });

    return (
        <section id="about" className="py-20 md:py-32 bg-muted/50">
            <div className="container px-4 md:px-6">
                <motion.div
                    ref={ref}
                    className="grid gap-10 lg:grid-cols-2 lg:gap-16 items-center"
                    initial={{ opacity: 0, y: 50 }}
                    animate={{
                        opacity: isInView ? 1 : 0,
                        y: isInView ? 0 : 50,
                    }}
                    transition={{ duration: 0.5, staggerChildren: 0.1 }}
                >
                    <div className="relative overflow-hidden rounded-xl">
                        <motion.div
                            initial={{ scale: 1.2, opacity: 0 }}
                            animate={{
                                scale: isInView ? 1 : 1.2,
                                opacity: isInView ? 1 : 0,
                            }}
                            transition={{ duration: 0.7 }}
                        >
                            <Image
                                src="/placeholder.svg?height=600&width=600"
                                alt="About Me"
                                width={600}
                                height={600}
                                className="object-cover w-full h-auto rounded-xl"
                            />
                        </motion.div>
                    </div>
                    <div className="space-y-6">
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{
                                opacity: isInView ? 1 : 0,
                                y: isInView ? 0 : 20,
                            }}
                            transition={{ duration: 0.5, delay: 0.2 }}
                        >
                            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">About Me</h2>
                            <div className="w-20 h-1 bg-primary mt-4 mb-6"></div>
                        </motion.div>

                        <motion.p
                            className="text-muted-foreground leading-relaxed"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{
                                opacity: isInView ? 1 : 0,
                                y: isInView ? 0 : 20,
                            }}
                            transition={{ duration: 0.5, delay: 0.3 }}
                        >
                            I am a dedicated Full Stack Developer pursuing Bachelor of Engineering in Artificial Intelligence & Data Science
                            at Savitribai Phule Pune University (DYPIEMR). With a strong academic record (GPA: 8.67) and hands-on industry
                            experience, I specialize in developing innovative web applications and AI-powered solutions.
                        </motion.p>

                        <motion.p
                            className="text-muted-foreground leading-relaxed"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{
                                opacity: isInView ? 1 : 0,
                                y: isInView ? 0 : 20,
                            }}
                            transition={{ duration: 0.5, delay: 0.4 }}
                        >
                            My professional experience includes serving as a Full Stack Developer Intern at SUMAGO INFOTECH PVT LTD, where I
                            successfully led the development of a comprehensive E-Commerce platform using the MERN stack. In my role as
                            team leader, I orchestrated the complete software development lifecycle and implemented strategic database
                            optimizations that enhanced system performance and user experience.
                        </motion.p>

                        <motion.p
                            className="text-muted-foreground leading-relaxed"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{
                                opacity: isInView ? 1 : 0,
                                y: isInView ? 0 : 20,
                            }}
                            transition={{ duration: 0.5, delay: 0.5 }}
                        >
                            My technical expertise encompasses a comprehensive range of technologies including Python, C++, JavaScript,
                            React.js, Next.js, and advanced machine learning frameworks. I maintain an active presence in competitive
                            programming with 200+ problems solved on LeetCode and have participated in prestigious hackathons including
                            Smart India Hackathon. I am committed to developing cutting-edge AI solutions and contributing to the
                            open-source community.
                        </motion.p>

                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{
                                opacity: isInView ? 1 : 0,
                                y: isInView ? 0 : 20,
                            }}
                            transition={{ duration: 0.5, delay: 0.6 }}
                        >
                            <Button className="mt-4 group">
                                Download Resume
                                <FileDown className="ml-2 h-4 w-4 transition-transform group-hover:translate-y-1" />
                            </Button>
                        </motion.div>
                    </div>
                </motion.div>
            </div>
        </section>
    );
}