"use client";

import { motion, useInView, AnimatePresence } from "framer-motion";
import { useRef } from "react";
import { Button } from "@/components/ui/button";
import { FileDown, Award, Code, Cpu, Database } from "lucide-react";
import Image from "next/image";

export default function About() {
    const ref = useRef(null);
    const isInView = useInView(ref, { once: true, margin: "-100px" });

    const skills = [
        { name: "Full Stack Dev", icon: <Code className="w-5 h-5" /> },
        { name: "AI/ML", icon: <Cpu className="w-5 h-5" /> },
        { name: "Data Science", icon: <Database className="w-5 h-5" /> },
        { name: "Competitive Coding", icon: <Award className="w-5 h-5" /> }
    ];

    return (
        <section id="about" className="py-20 md:py-32 bg-gradient-to-b from-muted/20 to-background relative overflow-hidden">
            {/* Decorative elements */}
            <div className="absolute top-0 left-0 w-full h-full overflow-hidden opacity-10">
                <div className="absolute -top-20 -left-20 w-64 h-64 rounded-full bg-primary/30 blur-3xl"></div>
                <div className="absolute bottom-0 right-0 w-96 h-96 rounded-full bg-secondary/30 blur-3xl"></div>
            </div>

            <div className="container px-4 md:px-6 relative">
                <motion.div
                    ref={ref}
                    className="grid gap-10 lg:grid-cols-2 lg:gap-16 items-center"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: isInView ? 1 : 0 }}
                    transition={{ duration: 0.5 }}
                >
                    <div className="relative overflow-hidden rounded-2xl border-2 border-primary/10 shadow-lg hover:shadow-xl transition-shadow">
                        <motion.div
                            initial={{ scale: 1.1, opacity: 0, rotate: 2 }}
                            animate={{
                                scale: isInView ? 1 : 1.1,
                                opacity: isInView ? 1 : 0,
                                rotate: isInView ? 0 : 2
                            }}
                            transition={{
                                duration: 0.7,
                                type: "spring",
                                damping: 10,
                                stiffness: 100
                            }}
                            className="relative aspect-square overflow-hidden"
                        >
                            <Image
                                src="/placeholder.svg?height=600&width=600"
                                alt="About Me"
                                width={600}
                                height={600}
                                className="object-cover w-full h-full hover:scale-105 transition-transform duration-500"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-background/70 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 flex items-end p-6">
                                <motion.p
                                    className="text-white text-lg font-medium"
                                    initial={{ y: 20 }}
                                    whileHover={{ y: 0 }}
                                >
                                    Passionate about creating innovative solutions
                                </motion.p>
                            </div>
                        </motion.div>
                    </div>

                    <div className="space-y-8">
                        <motion.div
                            initial={{ opacity: 0, y: 30 }}
                            animate={{
                                opacity: isInView ? 1 : 0,
                                y: isInView ? 0 : 30,
                            }}
                            transition={{
                                duration: 0.6,
                                type: "spring",
                                stiffness: 100
                            }}
                        >
                            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
                                About Me
                            </h2>
                            <div className="w-20 h-1.5 bg-gradient-to-r from-primary to-secondary mt-4 mb-6 rounded-full"></div>
                        </motion.div>

                        <AnimatePresence>
                            <motion.div
                                className="space-y-6"
                                initial={{ opacity: 0 }}
                                animate={{ opacity: isInView ? 1 : 0 }}
                                transition={{ staggerChildren: 0.1 }}
                            >
                                <motion.p
                                    className="text-muted-foreground leading-relaxed text-lg"
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{
                                        opacity: isInView ? 1 : 0,
                                        x: isInView ? 0 : -20,
                                    }}
                                    transition={{ duration: 0.5 }}
                                >
                                    I'm a <span className="text-primary font-medium">Full Stack Developer</span> and <span className="text-secondary font-medium">AI Engineer</span> currently pursuing my Bachelor's in Artificial Intelligence & Data Science at Savitribai Phule Pune University. With a strong academic foundation (GPA: 8.67) and hands-on industry experience, I bridge the gap between cutting-edge AI research and practical web application development.
                                </motion.p>

                                <motion.p
                                    className="text-muted-foreground leading-relaxed text-lg"
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{
                                        opacity: isInView ? 1 : 0,
                                        x: isInView ? 0 : -20,
                                    }}
                                    transition={{ duration: 0.5, delay: 0.1 }}
                                >
                                    As a <span className="font-medium">Full Stack Developer Intern</span> at SUMAGO INFOTECH, I led the development of a high-performance E-Commerce platform using the MERN stack. My leadership in the complete SDLC and database optimizations resulted in a <span className="font-medium">40% improvement</span> in system performance and enhanced user experience metrics.
                                </motion.p>

                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{
                                        opacity: isInView ? 1 : 0,
                                        y: isInView ? 0 : 20,
                                    }}
                                    transition={{ duration: 0.5, delay: 0.2 }}
                                    className="grid grid-cols-2 gap-4 pt-2"
                                >
                                    {skills.map((skill, index) => (
                                        <motion.div
                                            key={index}
                                            className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg border border-border hover:bg-muted transition-colors"
                                            whileHover={{ y: -3 }}
                                        >
                                            <div className="p-2 rounded-md bg-primary/10 text-primary">
                                                {skill.icon}
                                            </div>
                                            <span className="font-medium">{skill.name}</span>
                                        </motion.div>
                                    ))}
                                </motion.div>

                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{
                                        opacity: isInView ? 1 : 0,
                                        y: isInView ? 0 : 20,
                                    }}
                                    transition={{ duration: 0.5, delay: 0.3 }}
                                    className="pt-4"
                                >
                                    <Button className="group relative overflow-hidden">
                                        <span className="relative z-10 flex items-center">
                                            Download Resume
                                            <FileDown className="ml-2 h-4 w-4 transition-transform group-hover:translate-y-1" />
                                        </span>
                                        <span className="absolute inset-0 bg-gradient-to-r from-primary to-secondary opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                                    </Button>
                                </motion.div>
                            </motion.div>
                        </AnimatePresence>
                    </div>
                </motion.div>
            </div>
        </section>
    );
}