"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { Button } from "@/components/ui/button";
import ThemeToggle from "@/app/theme-toggle/page";

const links = [
    { name: "Home", href: "/" },
    { name: "About", href: "#about" },
    { name: "Projects", href: "#projects" },
    { name: "Skills", href: "#skills" },
    { name: "Contact", href: "#contact" },
];

export default function Header() {
    const pathname = usePathname();

    return (
        <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="container flex h-14 items-center justify-between">
                <div className="flex items-center gap-6">
                    <Link href="/" className="font-bold">
                        JD
                    </Link>
                    <nav className="hidden md:flex items-center gap-1">
                        {links.map((link) => (
                            <Button
                                key={link.name}
                                variant="ghost"
                                asChild
                                className={`${pathname === link.href ? "bg-accent" : ""}`}
                            >
                                <Link href={link.href}>{link.name}</Link>
                            </Button>
                        ))}
                    </nav>
                </div>
                <div className="flex items-center gap-2">
                    <ThemeToggle />
                    <Button size="sm" asChild>
                        <Link href="#contact">Contact</Link>
                    </Button>
                </div>
            </div>
        </header>
    );
}