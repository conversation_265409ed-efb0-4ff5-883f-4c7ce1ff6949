"use client";

import { useRef } from "react";
import { motion, useInView } from "framer-motion";
import { Code, Database, Globe, Layout, Server, Terminal, Smartphone, Layers, GitBranch, Cpu } from "lucide-react";

const skills = [
    {
        category: "Languages",
        icon: <Layout className="h-8 w-8 mb-4 text-primary" />,
        items: ["Python", "C++", "JavaScript", "HTML", "CSS", "SQL"],
    },
    {
        category: "Frameworks",
        icon: <Server className="h-8 w-8 mb-4 text-primary" />,
        items: ["React.js", "Next.js", "Node.js", "Bootstrap", "Tailwind CSS"],
    },
    {
        category: "Libraries",
        icon: <Database className="h-8 w-8 mb-4 text-primary" />,
        items: ["Pandas", "NumPy", "Scikit-learn", "Matplotlib"],
    },
    {
        category: "Tools & Platforms",
        icon: <Terminal className="h-8 w-8 mb-4 text-primary" />,
        items: ["Git", "GitHub", "VS Code", "Jupyter", "Linux", "Windows"],
    },
    {
        category: "Mobile",
        icon: <Smartphone className="h-8 w-8 mb-4 text-primary" />,
        items: ["React Native", "Flutter", "Ionic", "Expo", "Android", "iOS"],
    },
    {
        category: "Other",
        icon: <Code className="h-8 w-8 mb-4 text-primary" />,
        items: ["Git", "REST APIs", "Testing", "UI/UX Design", "Agile", "Scrum"],
    },
];

export default function Skills() {
    const ref = useRef(null);
    const isInView = useInView(ref, { once: true, margin: "-100px" });

    return (
        <section id="skills" className="py-20 md:py-32 bg-gradient-to-br from-muted/30 via-background to-muted/20 relative overflow-hidden">
            <div className="container px-4 md:px-6">
                <motion.div
                    ref={ref}
                    className="text-center mb-12 md:mb-16"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{
                        opacity: isInView ? 1 : 0,
                        y: isInView ? 0 : 20,
                    }}
                    transition={{ duration: 0.5 }}
                >
                    <motion.div
                        className="inline-block mb-4"
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{
                            opacity: isInView ? 1 : 0,
                            scale: isInView ? 1 : 0.8,
                        }}
                        transition={{ duration: 0.5, delay: 0.2 }}
                    >
                        <span className="px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium border border-primary/20">
                            Technical Expertise
                        </span>
                    </motion.div>
                    <h2 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
                        Skills & Technologies
                    </h2>
                    <p className="mt-6 text-muted-foreground md:text-xl max-w-3xl mx-auto leading-relaxed">
                        A comprehensive overview of my <span className="text-foreground font-medium">technical skills</span> and the
                        <span className="text-foreground font-medium"> cutting-edge technologies</span> I work with to build innovative solutions.
                    </p>
                    <div className="w-24 h-1 bg-gradient-to-r from-primary to-primary/50 mt-8 mx-auto rounded-full"></div>
                </motion.div>

                <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                    {skills.map((skill, index) => (
                        <motion.div
                            key={skill.category}
                            className="bg-gradient-to-br from-card to-card/50 rounded-xl p-8 shadow-lg border border-border/50 hover:shadow-xl hover:shadow-primary/5 transition-all duration-500 group hover:scale-105"
                            initial={{ opacity: 0, y: 50 }}
                            animate={{
                                opacity: isInView ? 1 : 0,
                                y: isInView ? 0 : 50,
                            }}
                            transition={{ duration: 0.6, delay: index * 0.15 }}
                        >
                            <div className="text-center mb-4">
                                {skill.icon}
                                <h3 className="text-xl font-bold">{skill.category}</h3>
                            </div>
                            <div className="grid grid-cols-2 gap-2">
                                {skill.items.map((item) => (
                                    <div key={item} className="flex items-center gap-2">
                                        <div className="h-2 w-2 rounded-full bg-primary"></div>
                                        <span>{item}</span>
                                    </div>
                                ))}
                            </div>
                        </motion.div>
                    ))}
                </div>

                <motion.div
                    className="mt-16 grid gap-8 md:grid-cols-4"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{
                        opacity: isInView ? 1 : 0,
                        y: isInView ? 0 : 20,
                    }}
                    transition={{ duration: 0.5, delay: 0.6 }}
                >
                    <div className="text-center p-6">
                        <Globe className="h-10 w-10 mx-auto mb-4 text-primary" />
                        <h3 className="text-xl font-bold mb-2">Web Development</h3>
                        <p className="text-muted-foreground">Building responsive and performant web applications</p>
                    </div>
                    <div className="text-center p-6">
                        <Layers className="h-10 w-10 mx-auto mb-4 text-primary" />
                        <h3 className="text-xl font-bold mb-2">Full Stack</h3>
                        <p className="text-muted-foreground">End-to-end development from frontend to backend</p>
                    </div>
                    <div className="text-center p-6">
                        <GitBranch className="h-10 w-10 mx-auto mb-4 text-primary" />
                        <h3 className="text-xl font-bold mb-2">Version Control</h3>
                        <p className="text-muted-foreground">Efficient code management and collaboration</p>
                    </div>
                    <div className="text-center p-6">
                        <Cpu className="h-10 w-10 mx-auto mb-4 text-primary" />
                        <h3 className="text-xl font-bold mb-2">Performance</h3>
                        <p className="text-muted-foreground">Optimizing applications for speed and efficiency</p>
                    </div>
                </motion.div>
            </div>
        </section>
    );
}