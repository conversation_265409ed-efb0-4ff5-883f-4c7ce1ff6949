"use client";

import { useRef } from "react";
import { motion, useInView } from "framer-motion";
import { Code, Database, Globe, Layout, Server, Terminal, Smartphone, Layers, GitBranch, Cpu } from "lucide-react";

const skills = [
    {
        category: "Frontend",
        icon: <Layout className="h-8 w-8 mb-4 text-primary" />,
        items: ["React", "Next.js", "TypeScript", "Tailwind CSS", "Framer Motion", "Redux"],
    },
    {
        category: "Backend",
        icon: <Server className="h-8 w-8 mb-4 text-primary" />,
        items: ["Node.js", "Express", "NestJS", "Python", "Django", "GraphQL"],
    },
    {
        category: "Database",
        icon: <Database className="h-8 w-8 mb-4 text-primary" />,
        items: ["MongoDB", "PostgreSQL", "MySQL", "Redis", "Prisma", "Supabase"],
    },
    {
        category: "DevOps",
        icon: <Terminal className="h-8 w-8 mb-4 text-primary" />,
        items: ["Docker", "Kubernetes", "AWS", "CI/CD", "GitHub Actions", "Vercel"],
    },
    {
        category: "Mobile",
        icon: <Smartphone className="h-8 w-8 mb-4 text-primary" />,
        items: ["React Native", "Flutter", "Ionic", "Expo", "Android", "iOS"],
    },
    {
        category: "Other",
        icon: <Code className="h-8 w-8 mb-4 text-primary" />,
        items: ["Git", "REST APIs", "Testing", "UI/UX Design", "Agile", "Scrum"],
    },
];

export default function Skills() {
    const ref = useRef(null);
    const isInView = useInView(ref, { once: true, margin: "-100px" });

    return (
        <section id="skills" className="py-20 md:py-32 bg-muted/50">
            <div className="container px-4 md:px-6">
                <motion.div
                    ref={ref}
                    className="text-center mb-12 md:mb-16"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{
                        opacity: isInView ? 1 : 0,
                        y: isInView ? 0 : 20,
                    }}
                    transition={{ duration: 0.5 }}
                >
                    <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Skills & Technologies</h2>
                    <p className="mt-4 text-muted-foreground md:text-xl max-w-3xl mx-auto">
                        A comprehensive overview of my technical skills and the technologies I work with.
                    </p>
                    <div className="w-20 h-1 bg-primary mt-6 mx-auto"></div>
                </motion.div>

                <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                    {skills.map((skill, index) => (
                        <motion.div
                            key={skill.category}
                            className="bg-card rounded-lg p-6 shadow-sm border"
                            initial={{ opacity: 0, y: 50 }}
                            animate={{
                                opacity: isInView ? 1 : 0,
                                y: isInView ? 0 : 50,
                            }}
                            transition={{ duration: 0.5, delay: index * 0.1 }}
                        >
                            <div className="text-center mb-4">
                                {skill.icon}
                                <h3 className="text-xl font-bold">{skill.category}</h3>
                            </div>
                            <div className="grid grid-cols-2 gap-2">
                                {skill.items.map((item) => (
                                    <div key={item} className="flex items-center gap-2">
                                        <div className="h-2 w-2 rounded-full bg-primary"></div>
                                        <span>{item}</span>
                                    </div>
                                ))}
                            </div>
                        </motion.div>
                    ))}
                </div>

                <motion.div
                    className="mt-16 grid gap-8 md:grid-cols-4"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{
                        opacity: isInView ? 1 : 0,
                        y: isInView ? 0 : 20,
                    }}
                    transition={{ duration: 0.5, delay: 0.6 }}
                >
                    <div className="text-center p-6">
                        <Globe className="h-10 w-10 mx-auto mb-4 text-primary" />
                        <h3 className="text-xl font-bold mb-2">Web Development</h3>
                        <p className="text-muted-foreground">Building responsive and performant web applications</p>
                    </div>
                    <div className="text-center p-6">
                        <Layers className="h-10 w-10 mx-auto mb-4 text-primary" />
                        <h3 className="text-xl font-bold mb-2">Full Stack</h3>
                        <p className="text-muted-foreground">End-to-end development from frontend to backend</p>
                    </div>
                    <div className="text-center p-6">
                        <GitBranch className="h-10 w-10 mx-auto mb-4 text-primary" />
                        <h3 className="text-xl font-bold mb-2">Version Control</h3>
                        <p className="text-muted-foreground">Efficient code management and collaboration</p>
                    </div>
                    <div className="text-center p-6">
                        <Cpu className="h-10 w-10 mx-auto mb-4 text-primary" />
                        <h3 className="text-xl font-bold mb-2">Performance</h3>
                        <p className="text-muted-foreground">Optimizing applications for speed and efficiency</p>
                    </div>
                </motion.div>
            </div>
        </section>
    );
}