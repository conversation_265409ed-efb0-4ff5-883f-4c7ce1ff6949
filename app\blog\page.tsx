"use client";

import { useRef } from "react";
import { motion, useInView } from "framer-motion";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CalendarDays, Clock, ArrowRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import PlaceholderImage from "@/components/ui/placeholder-image";

const blogPosts = [
    {
        id: 1,
        title: "NPTEL Python Programming Certification",
        excerpt: "Completed a comprehensive course on Python programming fundamentals through NPTEL, strengthening my foundation in one of the most versatile programming languages.",
        image: "data:image/svg+xml,%3Csvg width='600' height='400' viewBox='0 0 600 400' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='600' height='400' fill='%23F3F4F6'/%3E%3Cpath d='M280,180 h40 v40 h-40 v-40 Z' fill='%23E5E7EB'/%3E%3C/svg%3E",
        date: "April 2022",
        readTime: "Certification",
        category: "Python",
        slug: "nptel-python-certification",
    },
    {
        id: 2,
        title: "Frontend Development Mastery",
        excerpt: "Gained proficiency in front-end web development with comprehensive training in CSS, Bootstrap, and JavaScript, building a strong foundation for modern web development.",
        image: "/placeholder.svg?height=400&width=600",
        date: "January 2024",
        readTime: "Certification",
        category: "Web Development",
        slug: "frontend-development-certification",
    },
    {
        id: 3,
        title: "Competitive Programming Journey",
        excerpt: "Achieved 200+ problems solved on LeetCode and 300+ across various platforms. Active participant in coding competitions including Smart India Hackathon and Data Wizard Competition.",
        image: "/placeholder.svg?height=400&width=600",
        date: "Ongoing",
        readTime: "Achievement",
        category: "Competitive Programming",
        slug: "competitive-programming-achievements",
    },
    {
        id: 4,
        title: "Open Source Contributions",
        excerpt: "Contributed to open source projects during Hacktoberfest 2023, demonstrating commitment to the developer community and collaborative software development.",
        image: "/placeholder.svg?height=400&width=600",
        date: "October 2023",
        readTime: "Achievement",
        category: "Open Source",
        slug: "hacktoberfest-contributions",
    },
];

export default function Blog() {
    const ref = useRef(null);
    const isInView = useInView(ref, { once: true, margin: "-100px" });

    return (
        <section id="blog" className="py-20 md:py-32 bg-muted/50">
            <div className="container px-4 md:px-6">
                <motion.div
                    ref={ref}
                    className="text-center mb-12 md:mb-16"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{
                        opacity: isInView ? 1 : 0,
                        y: isInView ? 0 : 20,
                    }}
                    transition={{ duration: 0.5 }}
                >
                    <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Certifications & Achievements</h2>
                    <p className="mt-4 text-muted-foreground md:text-xl max-w-3xl mx-auto">
                        My professional certifications, competitive programming achievements, and contributions to the tech community.
                    </p>
                    <div className="w-20 h-1 bg-primary mt-6 mx-auto"></div>
                </motion.div>

                <div className="grid gap-8 md:grid-cols-3">
                    {blogPosts.map((post, index) => (
                        <motion.article
                            key={post.id}
                            initial={{ opacity: 0, y: 50 }}
                            animate={{
                                opacity: isInView ? 1 : 0,
                                y: isInView ? 0 : 50,
                            }}
                            transition={{ duration: 0.5, delay: index * 0.1 }}
                        >
                            <Card className="h-full overflow-hidden">
                                <div className="relative aspect-video overflow-hidden">
                                    <Image
                                        src={post.image}
                                        alt={post.title}
                                        fill
                                        className="object-cover transition-transform duration-300 hover:scale-105"
                                        priority={index < 2}
                                    />
                                </div>
                                <CardHeader>
                                    <div className="flex items-center gap-2 mb-2">
                                        <Badge variant="secondary">{post.category}</Badge>
                                        <div className="text-xs text-muted-foreground flex items-center gap-1">
                                            <CalendarDays className="h-3 w-3" />
                                            {post.date}
                                        </div>
                                        <div className="text-xs text-muted-foreground flex items-center gap-1">
                                            <Clock className="h-3 w-3" />
                                            {post.readTime}
                                        </div>
                                    </div>
                                    <CardTitle className="line-clamp-2">{post.title}</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-muted-foreground line-clamp-3">{post.excerpt}</p>
                                </CardContent>
                                <CardFooter>
                                    <Button variant="ghost" className="p-0 h-auto group" asChild>
                                        <Link href={`/blog/${post.slug}`}>
                                            Read More
                                            <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                                        </Link>
                                    </Button>
                                </CardFooter>
                            </Card>
                        </motion.article>
                    ))}
                </div>

                <motion.div
                    className="text-center mt-12"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{
                        opacity: isInView ? 1 : 0,
                        y: isInView ? 0 : 20,
                    }}
                    transition={{ duration: 0.5, delay: 0.6 }}
                >
                    <Button variant="outline" size="lg" asChild>
                        <Link href="/blog">
                            View All Articles
                            <ArrowRight className="ml-2 h-4 w-4" />
                        </Link>
                    </Button>
                </motion.div>
            </div>
        </section>
    );
}