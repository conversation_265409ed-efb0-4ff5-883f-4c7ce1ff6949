"use client";

import { motion, useScroll } from "framer-motion";

export default function ScrollProgress() {
    const { scrollYProgress } = useScroll();

    return (
        <motion.div
            className="fixed top-0 left-0 right-0 h-1 bg-primary/20 z-50"
            style={{ scaleX: scrollYProgress }}
            initial={{ scaleX: 0 }}
            transformOrigin="0%"
        >
            <motion.div
                className="h-full bg-gradient-to-r from-primary to-primary/80"
                style={{ scaleX: scrollYProgress }}
                initial={{ scaleX: 0 }}
                transformOrigin="0%"
            />
        </motion.div>
    );
}
